/**
 * Test file for table reload functionality
 * This file contains manual test scenarios to verify the reload feature works correctly
 */

import { reloadTableData, transformReloadedData } from './tableReloadApi.js'

// Mock data for testing
const mockApiResponse = {
  table_info: {
    'column1': {
      dtype: 'string',
      ftype: 'categorical',
      missing_count: 0,
      outlier_count: 0,
      unique_values: ['A', 'B', 'C']
    },
    'column2': {
      dtype: 'number',
      ftype: 'numerical',
      missing_count: 1,
      outlier_count: 2,
      unique_values: []
    }
  },
  table_data: {
    'column1': ['A', 'B', 'C', 'A'],
    'column2': [1, 2, 3, null]
  }
}

/**
 * Test the transformReloadedData function
 */
function testTransformReloadedData() {
  console.log('Testing transformReloadedData function...')
  
  try {
    const result = transformReloadedData(mockApiResponse, 'test_table_123')
    
    console.log('Transform result:', result)
    
    // Verify structure
    if (!result.columns || !result.rows || !result.table_id) {
      throw new Error('Missing required properties in transformed data')
    }
    
    // Verify columns
    if (result.columns.length !== 2) {
      throw new Error(`Expected 2 columns, got ${result.columns.length}`)
    }
    
    // Verify rows
    if (result.rows.length !== 4) {
      throw new Error(`Expected 4 rows, got ${result.rows.length}`)
    }
    
    // Verify table_id
    if (result.table_id !== 'test_table_123') {
      throw new Error(`Expected table_id 'test_table_123', got '${result.table_id}'`)
    }
    
    // Verify row structure
    const firstRow = result.rows[0]
    if (firstRow.column1 !== 'A' || firstRow.column2 !== 1) {
      throw new Error('Row data transformation failed')
    }
    
    console.log('✅ transformReloadedData test passed')
    return true
    
  } catch (error) {
    console.error('❌ transformReloadedData test failed:', error.message)
    return false
  }
}

/**
 * Test error handling in transformReloadedData
 */
function testTransformErrorHandling() {
  console.log('Testing error handling in transformReloadedData...')
  
  try {
    // Test with invalid data
    transformReloadedData(null, 'test_table')
    console.error('❌ Should have thrown error for null data')
    return false
  } catch (error) {
    console.log('✅ Correctly handled null data error')
  }
  
  try {
    // Test with missing table_data
    transformReloadedData({ table_info: {} }, 'test_table')
    console.error('❌ Should have thrown error for missing table_data')
    return false
  } catch (error) {
    console.log('✅ Correctly handled missing table_data error')
  }
  
  console.log('✅ Error handling tests passed')
  return true
}

/**
 * Manual test instructions for the full reload functionality
 */
function printManualTestInstructions() {
  console.log(`
🧪 MANUAL TEST INSTRUCTIONS FOR TABLE RELOAD FUNCTIONALITY

Prerequisites:
1. Ensure the backend /reload_data endpoint is available
2. Have at least one table with a valid table_id in the application
3. Open the browser developer console to see logs

Test Scenarios:

1. AUTOMATIC RELOAD ON TABLE SWITCH:
   - Load data using "一键取数" to create a table with table_id
   - Create or switch to another table
   - Switch back to the original table
   - Expected: Data should be automatically cleared and reloaded
   - Check console for reload logs

2. MANUAL RELOAD BUTTON:
   - Hover over a table tab that has table_id (from API data)
   - Look for the green reload button (🔄) in the sheet actions
   - Click the reload button
   - Expected: Toast notification showing reload progress and success/failure

3. ERROR HANDLING:
   - Try reloading with invalid table_id (modify in browser dev tools)
   - Expected: Error toast with appropriate message
   - Try reloading when backend is unavailable
   - Expected: Network error handling

4. UI BEHAVIOR:
   - Reload button should only appear for tables with table_id
   - Local imported tables should not show reload button
   - Loading states should be shown during reload

5. DATA INTEGRITY:
   - After reload, verify data matches backend source
   - Check that table metadata (name, etc.) is preserved
   - Verify that table structure (columns, rows) is correct

Expected Console Logs:
- "Switching from sheet X to Y"
- "Clearing data for previous sheet X"
- "Reloading data for sheet Y with table_id: ..."
- "Successfully reloaded data for sheet Y"

Expected Toast Messages:
- "正在重新加载表格数据..." (info)
- "表格数据重新加载成功" (success)
- Error messages for failures (error)
`)
}

// Run tests if this file is executed directly
if (typeof window !== 'undefined') {
  console.log('🧪 Running Table Reload API Tests...')
  
  const test1 = testTransformReloadedData()
  const test2 = testTransformErrorHandling()
  
  if (test1 && test2) {
    console.log('✅ All automated tests passed!')
  } else {
    console.log('❌ Some tests failed')
  }
  
  printManualTestInstructions()
}

export { testTransformReloadedData, testTransformErrorHandling, printManualTestInstructions }
