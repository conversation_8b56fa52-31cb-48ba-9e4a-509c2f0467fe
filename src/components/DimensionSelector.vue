<template>
  <div class="dimension-selector">
    <div class="selector-body">
      <div class="category-levels">
        <div class="level-label">一级分类</div>
        <div class="level level-1">
          <div v-if="isLoadingCategories" class="loading-item">加载分类中...</div>
          <div
            v-for="cat1 in level1Categories"
            :key="cat1.id"
            :class="['cat-item', { active: cat1.id === selectedCat1 }]"
            @click="selectCat1(cat1.id)"
          >
            {{ cat1.name }}
          </div>
          <div v-if="!isLoadingCategories && level1Categories.length === 0" class="empty-item">
            暂无可用分类
          </div>
        </div>
        <div v-if="level2Categories.length">
          <div class="level-label">二级分类</div>
          <div class="level level-2">
            <div
              v-for="cat2 in level2Categories"
              :key="cat2.id"
              :class="['cat-item', { active: cat2.id === selectedCat2 }]"
              @click="selectCat2(cat2.id)"
            >
              {{ cat2.name }}
            </div>
          </div>
        </div>
        <div v-if="level3Categories.length">
          <div class="level-label">三级分类</div>
          <div class="level level-3">
            <div
              v-for="cat3 in level3Categories"
              :key="cat3.id"
              :class="['cat-item', { active: cat3.id === selectedCat3 }]"
              @click="selectCat3(cat3.id)"
            >
              {{ cat3.name }}
            </div>
          </div>
        </div>
      </div>
      <div class="dimension-list" v-if="dimensionList.length">
        <div v-if="isLoadingDimensions" class="loading-item">加载维度中...</div>
        <div
          v-for="dim in dimensionList"
          :key="dim.id"
          class="dimension-item"
          @click="$emit('select', {
            id: dim.id,
            name: dim.display_name,
            originalData: dim
          })"
        >
          <span>{{ dim.display_name }}</span>
        </div>
      </div>
      <div v-else class="empty-tip">
        <div v-if="isLoadingDimensions">加载维度中...</div>
        <div v-else-if="!selectedCat1">请选择分类以查看维度</div>
        <div v-else>该分类下暂无可用维度</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useStates } from '../store/states'
import { fetchCategories, fetchDimensionsByCategory } from '../utils/categoryApi'

const statesStore = useStates()
const {
  availableCategories,
  availableDimensions
} = storeToRefs(statesStore)

// Loading states
const isLoadingCategories = ref(false)
const isLoadingDimensions = ref(false)

// Local selection state - now using IDs for parent-child relationships
const selectedCat1 = ref(null) // stores category ID
const selectedCat2 = ref(null) // stores category ID
const selectedCat3 = ref(null) // stores category ID

// Get granularity and indicators from store
const granularityComponent = computed(() => {
  return statesStore.states.find((state) => state.componentName === '粒度')
})

const indicatorComponent = computed(() => {
  return statesStore.states.find((state) => state.componentName === '目标指标')
})

// Computed categories organized by levels - flat array filtering
// Note: API returns level as string and uses empty string for root parent
const level1Categories = computed(() => {
  const allCategories = availableCategories.value || []
  return allCategories.filter(cat => cat.level === '1')
})

const level2Categories = computed(() => {
  if (!selectedCat1.value) return []
  const allCategories = availableCategories.value || []
  return allCategories.filter(cat => cat.level === '2' && cat.parent === selectedCat1.value)
})

const level3Categories = computed(() => {
  if (!selectedCat2.value) return []
  const allCategories = availableCategories.value || []
  return allCategories.filter(cat => cat.level === '3')
})

const dimensionList = computed(() => {
  return availableDimensions.value || []
})

// Helper function to get index keys
const getIndexKey = (index) => {
  if (typeof index === 'object' && index.indexId) {
    return index.indexId
  }
  return typeof index === 'string' ? index : index.indexName
}

// Helper function to get category name by ID
const getCategoryNameById = (categoryId) => {
  if (!categoryId) return ''
  const allCategories = availableCategories.value || []
  const category = allCategories.find(cat => cat.id === categoryId)
  return category ? category.name : ''
}

// Fetch categories when component mounts
onMounted(async () => {
  await loadCategories()
})

// Load categories from API
const loadCategories = async () => {
  const granularityId = granularityComponent.value?.granularityId
  if (!granularityId) {
    console.log('No granularity selected, skipping categories fetch')
    return
  }

  try {
    isLoadingCategories.value = true
    const categories = await fetchCategories(granularityId)
    statesStore.setAvailableCategories(categories)
    console.log('Categories loaded for dimensions:', categories)
  } catch (error) {
    console.error('Failed to load categories for dimensions:', error)
  } finally {
    isLoadingCategories.value = false
  }
}

// Load dimensions based on selected category
const loadDimensions = async () => {
  const granularityId = granularityComponent.value?.granularityId
  if (!granularityId || !selectedCat1.value) {
    console.log('Missing granularity or category, skipping dimensions fetch')
    return
  }

  try {
    isLoadingDimensions.value = true

    // Get selected indicator IDs for dimension filtering
    const indices = indicatorComponent.value?.indices?.map((idx) => getIndexKey(idx)) || []

    // Convert category IDs to names for API call
    const cat1Name = getCategoryNameById(selectedCat1.value)
    const cat2Name = getCategoryNameById(selectedCat2.value)
    const cat3Name = getCategoryNameById(selectedCat3.value)

    const dimensions = await fetchDimensionsByCategory(
      granularityId,
      cat1Name,
      cat2Name,
      cat3Name,
      indices
    )
    statesStore.setAvailableDimensions(dimensions)

    // Update selected categories in store using names
    statesStore.setSelectedCategories('dimensions', {
      category_lv_1: cat1Name,
      category_lv_2: cat2Name,
      category_lv_3: cat3Name
    })

    console.log('Dimensions loaded for category:', {
      category_lv_1: cat1Name,
      category_lv_2: cat2Name,
      category_lv_3: cat3Name,
      indices,
      dimensions
    })
  } catch (error) {
    console.error('Failed to load dimensions:', error)
  } finally {
    isLoadingDimensions.value = false
  }
}

function selectCat1(categoryId) {
  selectedCat1.value = categoryId
  selectedCat2.value = null
  selectedCat3.value = null
  // Clear dimensions when changing category
  statesStore.setAvailableDimensions([])
  // Load dimensions for the selected category
  loadDimensions()
}

function selectCat2(categoryId) {
  selectedCat2.value = categoryId
  selectedCat3.value = null
  // Clear dimensions when changing category
  statesStore.setAvailableDimensions([])
  // Load dimensions for the selected category
  loadDimensions()
}

function selectCat3(categoryId) {
  selectedCat3.value = categoryId
  // Clear dimensions when changing category
  statesStore.setAvailableDimensions([])
  // Load dimensions for the selected category
  loadDimensions()
}

// Watch for granularity changes to reload categories
watch(() => granularityComponent.value?.granularityId, (newId, oldId) => {
  if (newId && newId !== oldId) {
    // Reset selections
    selectedCat1.value = null
    selectedCat2.value = null
    selectedCat3.value = null
    statesStore.setAvailableDimensions([])
    statesStore.clearSelectedCategories('dimensions')
    // Load new categories
    loadCategories()
  }
})

// Watch for indicator changes to reload dimensions
watch(() => indicatorComponent.value?.indices, (newIndices, oldIndices) => {
  // Only reload if we have a category selected and indicators actually changed
  if (selectedCat1.value && JSON.stringify(newIndices) !== JSON.stringify(oldIndices)) {
    loadDimensions()
  }
}, { deep: true })
</script>

<style scoped>
.dimension-selector {
  width: 100%;
  height: 100%;
  background: transparent;
  display: flex;
  flex-direction: column;
}
.selector-body {
  display: flex;
  flex-direction: row;
  flex: 1;
  padding: 20px;
  gap: 24px;
  overflow: hidden;
}
.category-levels {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  max-width: 220px;
}
.level-label {
  font-size: 0.98rem;
  font-weight: 500;
  color: #1976d2;
  margin: 8px 0 4px 0;
  padding-left: 2px;
}
.level {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 4px;
}
.cat-item {
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  background: #f7faff;
  color: #333;
  transition:
    background 0.2s,
    color 0.2s;
}
.cat-item.active {
  background: #1976d2;
  color: #fff;
}
.dimension-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 200px;
}
.dimension-item {
  padding: 10px 16px;
  border-radius: 6px;
  background: #f5f5f5;
  cursor: pointer;
  font-size: 1rem;
  color: #1976d2;
  transition:
    background 0.2s,
    color 0.2s;
}
.dimension-item:hover {
  background: #1976d2;
  color: #fff;
}
.empty-tip {
  color: #aaa;
  font-size: 1rem;
  margin-top: 40px;
}
.loading-item {
  color: #1976d2;
  font-size: 0.9rem;
  padding: 8px 12px;
  text-align: center;
  font-style: italic;
}
.empty-item {
  color: #aaa;
  font-size: 0.9rem;
  padding: 8px 12px;
  text-align: center;
  font-style: italic;
}
</style>
