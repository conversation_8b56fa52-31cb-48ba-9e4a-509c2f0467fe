<template>
  <div class="ribbon-button-group" :class="[orientation, { 'has-separator': separator }]">
    <slot></slot>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'

const props = defineProps({
  orientation: {
    type: String,
    default: 'horizontal',
    validator: (value) => ['horizontal', 'vertical'].includes(value)
  },
  separator: {
    type: Boolean,
    default: false
  }
})
</script>

<style scoped>
.ribbon-button-group {
  display: flex;
  gap: 2px;
  position: relative;
}

.ribbon-button-group.horizontal {
  flex-direction: row;
  align-items: center;
}

.ribbon-button-group.vertical {
  flex-direction: column;
  align-items: stretch;
}

.ribbon-button-group.has-separator::after {
  content: '';
  position: absolute;
  background: #e5e7eb;
}

.ribbon-button-group.horizontal.has-separator::after {
  right: -12px;
  top: 10%;
  bottom: 10%;
  width: 1px;
}

.ribbon-button-group.vertical.has-separator::after {
  bottom: -12px;
  left: 10%;
  right: 10%;
  height: 1px;
}

/* Grouped button styling */
.ribbon-button-group.horizontal :deep(.ribbon-button:not(:first-child)) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  margin-left: -1px;
}

.ribbon-button-group.horizontal :deep(.ribbon-button:not(:last-child)) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.ribbon-button-group.vertical :deep(.ribbon-button:not(:first-child)) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  margin-top: -1px;
}

.ribbon-button-group.vertical :deep(.ribbon-button:not(:last-child)) {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* Ensure proper z-index for hover states */
.ribbon-button-group :deep(.ribbon-button:hover) {
  z-index: 1;
  position: relative;
}
</style>
